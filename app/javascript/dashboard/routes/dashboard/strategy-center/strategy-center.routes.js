import { frontendURL } from '../../../helper/URLHelper';
import SettingsWrapper from '../settings/Wrapper.vue';

const StrategyCenterIndex = () =>
  import('./StrategyCenterIndex.vue');

export const routes = [
  {
    path: frontendURL('accounts/:accountId/strategy-center'),
    component: SettingsWrapper,
    props: {
      headerTitle: 'SIDEBAR.STRATEGY_CENTER',
      icon: 'i-lucide-git-branch',
      showNewButton: false,
    },
    children: [
      {
        path: '',
        name: 'strategy_center_index',
        component: StrategyCenterIndex,
        meta: {
          permissions: ['administrator', 'agent', 'custom_role'],
        },
      },
    ],
  },
];

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Icon from 'dashboard/components-next/icon/Icon.vue';

const { t } = useI18n();

const props = defineProps({
  branchCount: {
    type: Number,
    required: true
  },
  tagCount: {
    type: Number,
    required: true
  }
});

const formattedBranchCount = computed(() => {
  return props.branchCount.toLocaleString();
});

const formattedTagCount = computed(() => {
  return props.tagCount.toLocaleString();
});
</script>

<template>
  <div class="flex items-center gap-4 text-sm text-n-slate-11">
    <!-- 分支统计 -->
    <div class="flex items-center gap-1">
      <Icon 
        name="i-lucide-git-branch" 
        class="size-4" 
      />
      <span class="font-medium">{{ formattedBranchCount }}</span>
      <span>Branches</span>
    </div>
    
    <!-- 标签统计 -->
    <div class="flex items-center gap-1">
      <Icon 
        name="i-lucide-tag" 
        class="size-4" 
      />
      <span class="font-medium">{{ formattedTagCount }}</span>
      <span>Tags</span>
    </div>
  </div>
</template>

<style scoped>
/* 统计信息样式 */
.stats-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--n-slate-11);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-number {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .stats-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>

<script setup>
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  columns: {
    type: Array,
    required: true
  }
});
</script>

<template>
  <thead class="bg-n-slate-2 border-b border-n-weak">
    <tr>
      <th
        v-for="column in columns"
        :key="column.id"
        class="px-4 py-3 text-left text-sm font-medium text-n-slate-12 border-r border-n-weak last:border-r-0"
        :style="{ width: column.width, minWidth: column.width }"
      >
        {{ column.label }}
      </th>
    </tr>
  </thead>
</template>

<style scoped>
/* 表格头部样式 */
.table-header {
  background-color: var(--n-slate-2);
  border-bottom: 1px solid var(--n-weak);
}

.table-header th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--n-slate-12);
  border-right: 1px solid var(--n-weak);
}

.table-header th:last-child {
  border-right: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header th {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }
}
</style>

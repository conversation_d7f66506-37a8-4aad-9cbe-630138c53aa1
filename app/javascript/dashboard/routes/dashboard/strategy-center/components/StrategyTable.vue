<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import StrategyTableHeader from './StrategyTableHeader.vue';
import StrategyTableBody from './StrategyTableBody.vue';
import AddRowButton from './AddRowButton.vue';

const { t } = useI18n();

const props = defineProps({
  strategyRows: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['row-update', 'add-row']);

// 表格列定义
const tableColumns = computed(() => [
  { id: 'stage', label: '生命周期模型', type: 'text', width: '120px' },
  { id: 'trigger', label: '触发', type: 'text', width: '150px' },
  { id: 'target', label: '活动目标', type: 'text', width: '150px' },
  { id: 'strategy', label: '运营策略', type: 'richtext', width: '300px' },
  { id: 'agent', label: 'agent', type: 'tag', width: '120px' },
  { id: 'importance', label: '重要性', type: 'text', width: '120px' },
  { id: 'constraints', label: '限制条件', type: 'text', width: '200px' }
]);

const handleRowUpdate = (rowData) => {
  emit('row-update', rowData);
};

const handleAddRow = () => {
  emit('add-row');
};
</script>

<template>
  <div class="bg-white rounded-lg border border-n-weak shadow-sm overflow-hidden">
    <!-- 策略表格 -->
    <div class="overflow-x-auto">
      <table class="w-full border-collapse">
        <StrategyTableHeader :columns="tableColumns" />
        <StrategyTableBody
          :strategy-rows="strategyRows"
          :columns="tableColumns"
          @row-update="handleRowUpdate"
        />
      </table>
    </div>
    
    <!-- 新增行按钮 -->
    <div class="p-4 border-t border-n-weak bg-n-slate-1">
      <AddRowButton @click="handleAddRow" />
    </div>
  </div>
</template>

<style scoped>
/* 表格容器样式 */
.strategy-table-container {
  background: white;
  border-radius: 0.5rem;
  border: 1px solid var(--n-weak);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 表格样式 */
.strategy-table {
  width: 100%;
  border-collapse: collapse;
}

/* 滚动条样式 */
.table-scroll::-webkit-scrollbar {
  height: 8px;
}

.table-scroll::-webkit-scrollbar-track {
  background: var(--n-slate-2);
  border-radius: 4px;
}

.table-scroll::-webkit-scrollbar-thumb {
  background: var(--n-slate-6);
  border-radius: 4px;
}

.table-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--n-slate-8);
}

/* 新增行区域样式 */
.add-row-section {
  padding: 1rem;
  border-top: 1px solid var(--n-weak);
  background-color: var(--n-slate-1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategy-table-container {
    margin: 0 -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}
</style>

<script setup>
import { useI18n } from 'vue-i18n';
import StrategyTableRow from './StrategyTableRow.vue';

const { t } = useI18n();

const props = defineProps({
  strategyRows: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['row-update']);

const handleRowUpdate = (rowData) => {
  emit('row-update', rowData);
};
</script>

<template>
  <tbody class="bg-white divide-y divide-n-weak">
    <StrategyTableRow
      v-for="row in strategyRows"
      :key="row.id"
      :row-data="row"
      :columns="columns"
      @update="handleRowUpdate"
    />
  </tbody>
</template>

<style scoped>
/* 表格主体样式 */
.table-body {
  background-color: white;
  divide-y: 1px solid var(--n-weak);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-body {
    font-size: 0.875rem;
  }
}
</style>

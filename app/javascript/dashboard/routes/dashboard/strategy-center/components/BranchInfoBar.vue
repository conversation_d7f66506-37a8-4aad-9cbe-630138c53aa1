<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Button from 'dashboard/components-next/button/Button.vue';
import BranchSelector from './BranchSelector.vue';
import BranchStats from './BranchStats.vue';

const { t } = useI18n();

const props = defineProps({
  selectedBranch: {
    type: Object,
    required: true
  },
  branches: {
    type: Array,
    required: true
  },
  branchStats: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['branch-change', 'data-branch']);

const handleBranchChange = (branch) => {
  emit('branch-change', branch);
};

const handleDataBranch = () => {
  emit('data-branch');
};
</script>

<template>
  <div class="flex items-center justify-between p-4 mb-4 bg-white rounded-lg border border-n-weak shadow-sm">
    <!-- 左侧：分支选择器和统计信息 -->
    <div class="flex items-center gap-6">
      <!-- 分支选择器 -->
      <BranchSelector
        :selected-branch="selectedBranch"
        :branches="branches"
        @branch-change="handleBranchChange"
      />
      
      <!-- 分支统计 -->
      <BranchStats
        :branch-count="branchStats.branchCount"
        :tag-count="branchStats.tagCount"
      />
    </div>
    
    <!-- 右侧：数据分支按钮 -->
    <div class="flex items-center gap-2">
      <Button
        variant="solid"
        color="blue"
        size="sm"
        @click="handleDataBranch"
      >
        数据分支
      </Button>
    </div>
  </div>
</template>

<style scoped>
/* 分支信息栏样式 */
.branch-info-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid var(--n-weak);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .branch-info-container {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .branch-info-container > div:first-child {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .branch-info-container > div:last-child {
    justify-content: center;
  }
}
</style>

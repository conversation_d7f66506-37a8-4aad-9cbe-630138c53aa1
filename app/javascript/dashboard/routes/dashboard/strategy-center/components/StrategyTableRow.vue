<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import LifecycleStageCell from './cells/LifecycleStageCell.vue';
import TriggerCell from './cells/TriggerCell.vue';
import TargetCell from './cells/TargetCell.vue';
import StrategyCell from './cells/StrategyCell.vue';
import AgentTagCell from './cells/AgentTagCell.vue';
import ImportanceCell from './cells/ImportanceCell.vue';
import ConstraintCell from './cells/ConstraintCell.vue';

const { t } = useI18n();

const props = defineProps({
  rowData: {
    type: Object,
    required: true
  },
  columns: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['update']);

const handleCellUpdate = (field, value) => {
  const updatedRow = {
    ...props.rowData,
    [field]: value
  };
  emit('update', updatedRow);
};

// 根据列类型获取对应的单元格组件
const getCellComponent = (column) => {
  const componentMap = {
    'stage': LifecycleStageCell,
    'trigger': Trigger<PERSON>ell,
    'target': TargetCell,
    'strategy': StrategyCell,
    'agent': AgentTagCell,
    'importance': ImportanceCell,
    'constraints': ConstraintCell
  };
  
  return componentMap[column.id] || TriggerCell; // 默认使用文本单元格
};
</script>

<template>
  <tr class="hover:bg-n-slate-1 transition-colors">
    <td
      v-for="column in columns"
      :key="column.id"
      class="px-4 py-3 border-r border-n-weak last:border-r-0 align-top"
      :style="{ width: column.width, minWidth: column.width }"
    >
      <component
        :is="getCellComponent(column)"
        :value="rowData[column.id]"
        :column="column"
        @update="(value) => handleCellUpdate(column.id, value)"
      />
    </td>
  </tr>
</template>

<style scoped>
/* 表格行样式 */
.table-row {
  transition: background-color 0.15s ease-in-out;
}

.table-row:hover {
  background-color: var(--n-slate-1);
}

.table-row td {
  padding: 0.75rem 1rem;
  border-right: 1px solid var(--n-weak);
  vertical-align: top;
}

.table-row td:last-child {
  border-right: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-row td {
    padding: 0.5rem 0.75rem;
  }
}
</style>

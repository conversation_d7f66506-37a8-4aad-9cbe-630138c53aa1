<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  column: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

const isEditing = ref(false);
const editingValue = ref(props.value);

const displayValue = computed(() => {
  return props.value || '点击编辑策略内容';
});

const formattedStrategy = computed(() => {
  if (!props.value) return '';
  // 将 <br> 标签转换为换行符显示
  return props.value.replace(/<br\s*\/?>/gi, '\n');
});

const startEditing = () => {
  isEditing.value = true;
  // 编辑时将 <br> 转换为换行符
  editingValue.value = props.value.replace(/<br\s*\/?>/gi, '\n');
};

const saveStrategy = () => {
  // 保存时将换行符转换为 <br> 标签
  const htmlValue = editingValue.value.replace(/\n/g, '<br>');
  emit('update', htmlValue);
  isEditing.value = false;
};

const cancelEditing = () => {
  editingValue.value = props.value.replace(/<br\s*\/?>/gi, '\n');
  isEditing.value = false;
};

const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    cancelEditing();
  }
  // 允许 Enter 键换行，不自动保存
};
</script>

<template>
  <div class="min-h-[80px] flex items-start">
    <div
      v-if="!isEditing"
      class="w-full p-2 rounded cursor-pointer hover:bg-n-slate-2 transition-colors"
      @click="startEditing"
    >
      <div 
        v-if="value"
        class="text-sm text-n-slate-12 whitespace-pre-wrap break-words prose prose-sm max-w-none"
        v-html="value"
      />
      <span 
        v-else
        class="text-sm text-n-slate-10 italic"
      >
        {{ displayValue }}
      </span>
    </div>
    
    <div v-else class="w-full">
      <textarea
        v-model="editingValue"
        class="w-full p-2 text-sm border border-n-brand rounded focus:outline-none focus:ring-2 focus:ring-n-brand/20 resize-none"
        rows="5"
        placeholder="输入运营策略内容，支持换行..."
        @blur="saveStrategy"
        @keydown="handleKeydown"
        @mounted="$el.focus()"
      />
      <div class="flex justify-end gap-2 mt-2">
        <button
          class="px-3 py-1 text-xs text-n-slate-11 hover:text-n-slate-12 transition-colors"
          @click="cancelEditing"
        >
          取消
        </button>
        <button
          class="px-3 py-1 text-xs bg-n-brand text-white rounded hover:bg-n-brand/90 transition-colors"
          @click="saveStrategy"
        >
          保存
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 运营策略单元格样式 */
.strategy-cell {
  min-height: 80px;
  display: flex;
  align-items: flex-start;
}

.strategy-display {
  width: 100%;
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.strategy-display:hover {
  background-color: var(--n-slate-2);
}

.strategy-content {
  font-size: 0.875rem;
  color: var(--n-slate-12);
  white-space: pre-wrap;
  word-break: break-words;
}

.strategy-content.placeholder {
  color: var(--n-slate-10);
  font-style: italic;
}

.strategy-textarea {
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  border: 1px solid var(--n-brand);
  border-radius: 0.25rem;
  resize: none;
}

.strategy-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--n-brand-rgb), 0.2);
}

/* 富文本样式 */
.prose {
  max-width: none;
}

.prose :deep(ul) {
  list-style-type: disc;
  padding-left: 1.25rem;
}

.prose :deep(li) {
  margin: 0.25rem 0;
}

.prose :deep(strong) {
  font-weight: 600;
}

/* 编辑按钮样式 */
.edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.edit-button {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  transition: all 0.15s ease-in-out;
}

.edit-button.cancel {
  color: var(--n-slate-11);
}

.edit-button.cancel:hover {
  color: var(--n-slate-12);
}

.edit-button.save {
  background-color: var(--n-brand);
  color: white;
}

.edit-button.save:hover {
  background-color: rgba(var(--n-brand-rgb), 0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategy-content {
    font-size: 0.8125rem;
  }
  
  .strategy-textarea {
    font-size: 0.8125rem;
  }
}
</style>

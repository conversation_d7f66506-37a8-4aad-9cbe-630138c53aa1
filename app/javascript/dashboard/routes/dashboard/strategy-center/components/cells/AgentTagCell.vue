<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Badge from 'dashboard/components-next/badge/Badge.vue';
import FilterSelect from 'dashboard/components-next/filter/inputs/FilterSelect.vue';

const { t } = useI18n();

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  column: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

const isEditing = ref(false);
const selectedAgent = ref(null);

// 专家类型选项
const agentOptions = [
  { label: '获客专家', value: '获客专家' },
  { label: '互动专家', value: '互动专家' },
  { label: '转化专家', value: '转化专家' },
  { label: '关怀专家', value: '关怀专家' },
  { label: '召回专家', value: '召回专家' }
];

// 获取专家类型对应的颜色
const getAgentColor = (type) => {
  const colorMap = {
    '获客专家': 'blue',
    '互动专家': 'green', 
    '转化专家': 'orange',
    '关怀专家': 'purple',
    '召回专家': 'red'
  };
  return colorMap[type] || 'gray';
};

const displayValue = computed(() => {
  return props.value || '选择专家';
});

const startEditing = () => {
  isEditing.value = true;
  selectedAgent.value = agentOptions.find(option => option.value === props.value) || null;
};

const saveValue = () => {
  if (selectedAgent.value) {
    emit('update', selectedAgent.value.value);
  }
  isEditing.value = false;
};

const cancelEditing = () => {
  selectedAgent.value = null;
  isEditing.value = false;
};

const handleAgentChange = (option) => {
  selectedAgent.value = option;
  if (option) {
    emit('update', option.value);
  }
  isEditing.value = false;
};
</script>

<template>
  <div class="min-h-[40px] flex items-center">
    <div
      v-if="!isEditing"
      class="w-full p-2 rounded cursor-pointer hover:bg-n-slate-2 transition-colors"
      @click="startEditing"
    >
      <Badge
        v-if="value"
        :label="value"
        :color="getAgentColor(value)"
        variant="solid"
        size="sm"
      />
      <span 
        v-else
        class="text-sm text-n-slate-10 italic"
      >
        {{ displayValue }}
      </span>
    </div>
    
    <div v-else class="w-full">
      <FilterSelect
        v-model="selectedAgent"
        :options="agentOptions"
        placeholder="选择专家类型"
        size="sm"
        @update:model-value="handleAgentChange"
        @blur="cancelEditing"
      />
    </div>
  </div>
</template>

<style scoped>
/* 专家标签单元格样式 */
.agent-cell {
  min-height: 40px;
  display: flex;
  align-items: center;
}

.agent-display {
  width: 100%;
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.agent-display:hover {
  background-color: var(--n-slate-2);
}

.agent-placeholder {
  font-size: 0.875rem;
  color: var(--n-slate-10);
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-placeholder {
    font-size: 0.8125rem;
  }
}
</style>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  column: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

const isEditing = ref(false);
const editingValue = ref(props.value);

const displayValue = computed(() => {
  return props.value || '点击编辑';
});

const startEditing = () => {
  isEditing.value = true;
  editingValue.value = props.value;
};

const saveValue = () => {
  emit('update', editingValue.value);
  isEditing.value = false;
};

const cancelEditing = () => {
  editingValue.value = props.value;
  isEditing.value = false;
};

const handleKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    saveValue();
  } else if (event.key === 'Escape') {
    cancelEditing();
  }
};
</script>

<template>
  <div class="min-h-[60px] flex items-start">
    <div
      v-if="!isEditing"
      class="w-full p-2 rounded cursor-pointer hover:bg-n-slate-2 transition-colors"
      @click="startEditing"
    >
      <span 
        class="text-sm text-n-slate-12 whitespace-pre-wrap break-words"
        :class="{ 'text-n-slate-10 italic': !value }"
      >
        {{ displayValue }}
      </span>
    </div>
    
    <textarea
      v-else
      v-model="editingValue"
      class="w-full p-2 text-sm border border-n-brand rounded focus:outline-none focus:ring-2 focus:ring-n-brand/20 resize-none"
      rows="3"
      @blur="saveValue"
      @keydown="handleKeydown"
      @mounted="$el.focus()"
    />
  </div>
</template>

<style scoped>
/* 限制条件单元格样式 */
.constraint-cell {
  min-height: 60px;
  display: flex;
  align-items: flex-start;
}

.constraint-display {
  width: 100%;
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.constraint-display:hover {
  background-color: var(--n-slate-2);
}

.constraint-text {
  font-size: 0.875rem;
  color: var(--n-slate-12);
  white-space: pre-wrap;
  word-break: break-words;
}

.constraint-text.placeholder {
  color: var(--n-slate-10);
  font-style: italic;
}

.constraint-textarea {
  width: 100%;
  padding: 0.5rem;
  font-size: 0.875rem;
  border: 1px solid var(--n-brand);
  border-radius: 0.25rem;
  resize: none;
}

.constraint-textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--n-brand-rgb), 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .constraint-text {
    font-size: 0.8125rem;
  }
  
  .constraint-textarea {
    font-size: 0.8125rem;
  }
}
</style>

<script setup>
import { useI18n } from 'vue-i18n';
import Button from 'dashboard/components-next/button/Button.vue';
import Icon from 'dashboard/components-next/icon/Icon.vue';

const { t } = useI18n();

const emit = defineEmits(['click']);

const handleClick = () => {
  emit('click');
};
</script>

<template>
  <div class="flex justify-center">
    <Button
      variant="ghost"
      size="sm"
      class="text-n-slate-11 hover:text-n-slate-12 hover:bg-n-slate-2 transition-colors"
      @click="handleClick"
    >
      <Icon 
        name="i-lucide-plus" 
        class="mr-2 size-4" 
      />
      新增行
    </Button>
  </div>
</template>

<style scoped>
/* 新增行按钮样式 */
.add-row-container {
  display: flex;
  justify-content: center;
}

.add-row-button {
  color: var(--n-slate-11);
  transition: all 0.15s ease-in-out;
}

.add-row-button:hover {
  color: var(--n-slate-12);
  background-color: var(--n-slate-2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-row-button {
    width: 100%;
    justify-content: center;
  }
}
</style>

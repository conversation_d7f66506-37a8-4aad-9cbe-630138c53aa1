<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Button from 'dashboard/components-next/button/Button.vue';
import DropdownMenu from 'dashboard/components-next/dropdown-menu/DropdownMenu.vue';
import Icon from 'dashboard/components-next/icon/Icon.vue';
import Badge from 'dashboard/components-next/badge/Badge.vue';

const { t } = useI18n();

const props = defineProps({
  selectedBranch: {
    type: Object,
    required: true
  },
  branches: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['branch-change']);

const isOpen = ref(false);

const handleBranchSelect = (branch) => {
  emit('branch-change', branch);
  isOpen.value = false;
};

const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
};
</script>

<template>
  <div class="flex items-center gap-2">
    <!-- Git分支图标 -->
    <Icon 
      name="i-lucide-git-branch" 
      class="text-n-slate-11 size-4" 
    />
    
    <!-- 分支下拉选择器 -->
    <DropdownMenu>
      <template #trigger>
        <Button
          variant="ghost"
          size="sm"
          class="font-medium text-n-slate-12 hover:bg-n-slate-2"
          @click="toggleDropdown"
        >
          {{ selectedBranch.name }}
          <Icon 
            name="i-lucide-chevron-down" 
            class="ml-1 size-3" 
          />
        </Button>
      </template>
      
      <template #menu>
        <div class="py-1 min-w-48 bg-white rounded-lg shadow-lg border border-n-weak">
          <button
            v-for="branch in branches"
            :key="branch.id"
            class="flex items-center gap-2 w-full px-3 py-2 text-sm text-left hover:bg-n-slate-2 transition-colors"
            :class="{
              'bg-n-blue-2 text-n-blue-11': branch.id === selectedBranch.id
            }"
            @click="handleBranchSelect(branch)"
          >
            <Icon 
              name="i-lucide-git-branch" 
              class="size-4" 
            />
            <span class="flex-1">{{ branch.name }}</span>
            <Badge 
              v-if="branch.isDefault" 
              label="默认" 
              size="xs" 
              variant="soft"
              color="blue"
            />
          </button>
        </div>
      </template>
    </DropdownMenu>
  </div>
</template>

<style scoped>
/* 分支选择器样式 */
.branch-selector {
  position: relative;
}

.branch-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 50;
  margin-top: 0.25rem;
}

.branch-option {
  transition: all 0.15s ease-in-out;
}

.branch-option:hover {
  background-color: var(--n-slate-2);
}

.branch-option.active {
  background-color: var(--n-blue-2);
  color: var(--n-blue-11);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .branch-dropdown {
    min-width: 200px;
  }
}
</style>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import StrategyCenterToolbar from './components/StrategyCenterToolbar.vue';
import BranchInfoBar from './components/BranchInfoBar.vue';
import StrategyTable from './components/StrategyTable.vue';

const { t } = useI18n();

// 模拟数据
const selectedUser = ref({
  id: 1,
  name: 'Wade Cooper',
  avatar: null
});

const selectedBranch = ref({
  id: 1,
  name: 'master',
  isDefault: true
});

const branchStats = ref({
  branchCount: 198,
  tagCount: 341
});

// 策略表格数据
const strategyRows = ref([
  {
    id: 1,
    stage: '引入期',
    trigger: '初次接触',
    target: '吸引用户，引导完成关键行为',
    strategy: '欢迎SOP（自动介绍）<br>• 新人大礼包（介绍福利、品牌价值）<br>• 发放新人专享福利<br>• 营销内容（自动介绍、小贴士）',
    agent: '获客专家',
    importance: '正常介绍',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 2,
    stage: '成长期',
    trigger: '互动超过10次成熟期转化',
    target: '培养用户使用习惯，推进转化',
    strategy: '• 每日干货分享或活动讯息<br>• 使用指导（体验、用法）<br>• 1对1私信，按需分享',
    agent: '互动专家',
    importance: '正常介绍',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 3,
    stage: '成熟期',
    trigger: '用户活跃',
    target: '提升用户忠诚度，最大化用户价值',
    strategy: '• VIP会员体系，提供专属权益<br>• 新品优先体验<br>• 建立用户群（KOC群）<br>• 深度互动/合作',
    agent: '转化专家',
    importance: '正常介绍',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 4,
    stage: '衰退期',
    trigger: '超过7天未互动唤醒用户',
    target: '识别流失风险，主动干预，唤醒用户',
    strategy: '• 自动化推送（识别数据异常）<br>• 红包SOP（私域支持、问候语）<br>• 大额优惠券<br>• 限时利益刺激<br>• 用户调研，了解不活跃原因',
    agent: '关怀专家',
    importance: '徐水活跃',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  },
  {
    id: 5,
    stage: '流失期',
    trigger: '分析流失原因，指导产品优化，营销策略调整',
    target: '分析流失原因，指导产品优化，营销策略调整',
    strategy: '• 进行流失用户访谈（如果可行）<br>• 分析流失原因，适时更新策略<br>• 和新产品上线时，适当推送<br>• 优化营销策略，防止更多用户流失',
    agent: '召回专家',
    importance: '徐水活跃',
    constraints: '如果用户介绍策略基本相同，避免不断重复介绍用户，必须等客户再次进行互动'
  }
]);

const userOptions = computed(() => [
  { label: 'Wade Cooper', value: 1 },
  { label: 'Arlene Mccoy', value: 2 },
  { label: 'Devon Webb', value: 3 }
]);

const branches = computed(() => [
  { id: 1, name: 'master', isDefault: true },
  { id: 2, name: 'develop', isDefault: false },
  { id: 3, name: 'feature/strategy-v2', isDefault: false }
]);

// 事件处理
const handleUserChange = (user) => {
  selectedUser.value = user;
  console.log('User changed:', user);
};

const handleCreateTask = () => {
  console.log('Create task clicked');
};

const handleBranchChange = (branch) => {
  selectedBranch.value = branch;
  console.log('Branch changed:', branch);
};

const handleDataBranch = () => {
  console.log('Data branch clicked');
};

const handleRowUpdate = (rowData) => {
  console.log('Row updated:', rowData);
};

const handleAddRow = () => {
  const newRow = {
    id: Date.now(),
    stage: '新阶段',
    trigger: '',
    target: '',
    strategy: '',
    agent: '',
    importance: '',
    constraints: ''
  };
  strategyRows.value.push(newRow);
};
</script>

<template>
  <div class="flex flex-col w-full h-full overflow-hidden bg-n-background">
    <!-- 页面内容区域 - 居中布局 -->
    <main class="flex-1 px-6 overflow-y-auto">
      <div class="w-full max-w-[80rem] mx-auto py-4">
        <!-- 顶部工具栏 -->
        <StrategyCenterToolbar
          :selected-user="selectedUser"
          :user-options="userOptions"
          @user-change="handleUserChange"
          @create-task="handleCreateTask"
        />
        
        <!-- 分支信息栏 -->
        <BranchInfoBar
          :selected-branch="selectedBranch"
          :branches="branches"
          :branch-stats="branchStats"
          @branch-change="handleBranchChange"
          @data-branch="handleDataBranch"
        />
        
        <!-- 策略表格 -->
        <StrategyTable
          :strategy-rows="strategyRows"
          @row-update="handleRowUpdate"
          @add-row="handleAddRow"
        />
      </div>
    </main>
  </div>
</template>

<style scoped>
/* 确保页面内容居中显示 */
.strategy-center-container {
  max-width: 1280px; /* 80rem */
  margin: 0 auto;
  padding: 0 1.5rem; /* 24px */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategy-center-container {
    padding: 0 1rem; /* 16px */
  }
}
</style>

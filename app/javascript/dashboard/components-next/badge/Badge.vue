<script setup>
import { computed } from 'vue';

const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  size: {
    type: String,
    default: 'sm',
    validator: value => ['xs', 'sm', 'md', 'lg'].includes(value),
  },
  variant: {
    type: String,
    default: 'solid',
    validator: value => ['solid', 'soft', 'outline'].includes(value),
  },
  color: {
    type: String,
    default: 'slate',
    validator: value => 
      ['slate', 'blue', 'green', 'yellow', 'red', 'purple', 'pink', 'indigo'].includes(value),
  },
  rounded: {
    type: Boolean,
    default: true,
  },
});

const badgeClasses = computed(() => {
  const baseClasses = [
    'inline-flex',
    'items-center',
    'justify-center',
    'font-medium',
    'whitespace-nowrap',
    'transition-colors',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
  ];

  // Size classes
  const sizeClasses = {
    xs: ['text-xs', 'px-1.5', 'py-0.5', 'h-4'],
    sm: ['text-xs', 'px-2', 'py-1', 'h-5'],
    md: ['text-sm', 'px-2.5', 'py-1', 'h-6'],
    lg: ['text-sm', 'px-3', 'py-1.5', 'h-7'],
  };

  // Rounded classes
  const roundedClasses = props.rounded ? 'rounded-full' : 'rounded';

  // Color and variant classes
  const colorVariantClasses = {
    slate: {
      solid: 'bg-n-slate-9 text-n-slate-1 hover:bg-n-slate-10',
      soft: 'bg-n-slate-3 text-n-slate-11 hover:bg-n-slate-4',
      outline: 'border border-n-slate-6 text-n-slate-11 hover:bg-n-slate-2',
    },
    blue: {
      solid: 'bg-n-blue-9 text-n-blue-1 hover:bg-n-blue-10',
      soft: 'bg-n-blue-3 text-n-blue-11 hover:bg-n-blue-4',
      outline: 'border border-n-blue-6 text-n-blue-11 hover:bg-n-blue-2',
    },
    green: {
      solid: 'bg-green-500 text-white hover:bg-green-600',
      soft: 'bg-green-100 text-green-800 hover:bg-green-200',
      outline: 'border border-green-300 text-green-700 hover:bg-green-50',
    },
    yellow: {
      solid: 'bg-yellow-500 text-white hover:bg-yellow-600',
      soft: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
      outline: 'border border-yellow-300 text-yellow-700 hover:bg-yellow-50',
    },
    red: {
      solid: 'bg-red-500 text-white hover:bg-red-600',
      soft: 'bg-red-100 text-red-800 hover:bg-red-200',
      outline: 'border border-red-300 text-red-700 hover:bg-red-50',
    },
    purple: {
      solid: 'bg-purple-500 text-white hover:bg-purple-600',
      soft: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
      outline: 'border border-purple-300 text-purple-700 hover:bg-purple-50',
    },
    pink: {
      solid: 'bg-pink-500 text-white hover:bg-pink-600',
      soft: 'bg-pink-100 text-pink-800 hover:bg-pink-200',
      outline: 'border border-pink-300 text-pink-700 hover:bg-pink-50',
    },
    indigo: {
      solid: 'bg-indigo-500 text-white hover:bg-indigo-600',
      soft: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',
      outline: 'border border-indigo-300 text-indigo-700 hover:bg-indigo-50',
    },
  };

  return [
    ...baseClasses,
    ...sizeClasses[props.size],
    roundedClasses,
    colorVariantClasses[props.color][props.variant],
  ].join(' ');
});
</script>

<template>
  <span :class="badgeClasses">
    {{ label }}
  </span>
</template>

<style scoped>
/* Additional custom styles if needed */
.badge-enter-active,
.badge-leave-active {
  transition: all 0.2s ease;
}

.badge-enter-from,
.badge-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>

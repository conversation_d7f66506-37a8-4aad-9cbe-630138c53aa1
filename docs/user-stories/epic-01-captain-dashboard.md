# Epic: Captain - 综合看板

## 史诗概述
Captain综合看板是客服人员的主要工作界面，提供了实时的客户信息展示、AI辅助功能和智能回复建议，帮助客服人员高效地与客户进行沟通。

## 用户故事

### US-01-01: 用户信息概览展示
**优先级**: P0  
**估算**: S

**作为**客服人员  
**我想要**在综合看板上一眼看到当前客户的基本信息  
**以便**快速了解客户背景，提供个性化服务

**验收标准**：
- [ ] 显示客户头像（支持默认头像）
- [ ] 显示客户昵称
- [ ] 显示客户联系方式（手机号、邮箱等）
- [ ] 显示客户的主要标签（最多5个）
- [ ] 区分静态标签和动态标签的显示样式
- [ ] 信息加载时间不超过2秒
- [ ] 支持信息缺失时的优雅降级显示

---

### US-01-02: 用户生命周期可视化
**优先级**: P0  
**估算**: M

**作为**客服人员  
**我想要**通过步骤条组件直观地看到客户当前所处的生命周期阶段  
**以便**采取相应的服务策略和话术

**验收标准**：
- [ ] 设计美观的步骤条UI组件
- [ ] 支持至少5个生命周期阶段（如：新用户、活跃用户、沉睡用户、流失用户、忠诚用户）
- [ ] 当前阶段高亮显示
- [ ] 鼠标悬停显示阶段说明
- [ ] 支持阶段间的动画过渡效果
- [ ] 生命周期数据实时更新（延迟不超过5秒）

---

### US-01-03: 实时意图与情绪分析
**优先级**: P0  
**估算**: L

**作为**客服人员  
**我想要**实时了解客户的意图和情绪状态  
**以便**调整沟通策略，提供更好的服务体验

**验收标准**：
- [ ] 独立的意图分析组件设计
- [ ] 显示用户实时意图（如：咨询、投诉、购买、退款等）
- [ ] 显示意图置信度（百分比）
- [ ] 显示用户情绪类型（积极、中性、消极）
- [ ] 情绪置信度显示
- [ ] 根据情绪类型动态渲染组件边框颜色：
  - 积极：绿色
  - 中性：蓝色
  - 消极：红色
- [ ] 支持历史意图的快速查看入口
- [ ] 分析结果在用户消息发送后3秒内更新

---

### US-01-04: 智能回复建议
**优先级**: P0  
**估算**: L

**作为**客服人员  
**我想要**获得AI生成的多种回复建议  
**以便**快速、专业地回复客户，提高服务效率

**验收标准**：
- [ ] 每次提供3个不同策略的回复建议：
  - 策略1：专业正式
  - 策略2：友好亲切
  - 策略3：简洁高效
- [ ] UI类型区分：
  - 专业正式：蓝色边框，商务图标
  - 友好亲切：绿色边框，笑脸图标
  - 简洁高效：橙色边框，闪电图标
- [ ] 智能语义断句：
  - 优先按语义完整性断句，符合人类对话习惯
  - 单条消息建议控制在50字以内（非强制限制）
  - 断句规则优先级：
    1. 完整语义单元（一个完整的意思表达）
    2. 自然停顿点（句号、问号、感叹号）
    3. 逻辑分隔点（分号、逗号后的长停顿）
    4. 避免在词语中间、修饰关系中间断句
  - 保持消息间的逻辑连贯性和上下文关系
- [ ] 多条消息处理：
  - 需要多条消息时显示为单个建议卡片
  - 卡片内显示消息条数提示（如："3条消息"）
  - 预览时显示第一条消息内容 + "..."
  - 展开时显示所有消息的完整内容
- [ ] 每个建议卡片包含：
  - 策略标签和类型图标
  - 消息条数提示（如有多条）
  - 建议内容预览
  - "查看完整内容"链接（多条消息时）
  - "直接发送"按钮
  - "转到输入框"按钮
- [ ] 发送逻辑：
  - 单条消息：点击"直接发送"立即发送
  - 多条消息：点击"直接发送"按顺序发送所有消息（间隔1-2秒）
  - 支持发送过程中的取消操作
- [ ] 编辑功能：
  - 点击"转到输入框"将内容填充到输入框
  - 多条消息时可选择填充单条或全部
  - 支持逐条编辑和修改
- [ ] 建议生成时间不超过2秒
- [ ] 支持建议内容的复制功能
- [ ] 建议内容基于上下文动态生成
- [ ] 语义断句质量保证：
  - 断句后的每条消息应语义完整，能独立理解
  - 避免在关键信息点中间断句（如数字、时间、价格等）
  - 保持语气和情感的连贯性
  - 优先在自然停顿处断句，符合人类阅读习惯
  - 支持客服人员手动调整断句位置

**技术实现要点**：
```javascript
// 智能语义断句算法示例
function smartMessageSplit(content, suggestedMaxLength = 50) {
  if (content.length <= suggestedMaxLength) return [content];
  
  // 语义断句规则（按优先级）
  const breakPoints = [
    // 1. 完整句子结束
    { pattern: /[。！？]\s*/, priority: 1, description: '完整句子' },
    // 2. 分号分隔的并列句
    { pattern: /[；]\s*/, priority: 2, description: '并列关系' },
    // 3. 长逗号停顿（后跟连词或转折词）
    { pattern: /[，]\s*(?=但是|然而|不过|因此|所以|而且|并且)/, priority: 3, description: '逻辑转折' },
    // 4. 冒号后的解释说明
    { pattern: /[：]\s*/, priority: 4, description: '解释说明' },
    // 5. 一般逗号（较低优先级）
    { pattern: /[，]\s*/, priority: 5, description: '一般停顿' }
  ];
  
  function findBestBreakPoint(text, maxLength) {
    if (text.length <= maxLength) return text.length;
    
    let bestPoint = -1;
    let bestPriority = 999;
    
    for (let breakPoint of breakPoints) {
      const matches = [...text.matchAll(new RegExp(breakPoint.pattern.source, 'g'))];
      
      for (let match of matches) {
        const position = match.index + match[0].length;
        
        // 确保断点在合理范围内（不要太短或太长）
        if (position > maxLength * 0.3 && position <= maxLength) {
          if (breakPoint.priority < bestPriority) {
            bestPoint = position;
            bestPriority = breakPoint.priority;
          }
        }
      }
    }
    
    // 如果没找到合适的断点，在最大长度处强制断句
    return bestPoint > 0 ? bestPoint : maxLength;
  }
  
  const messages = [];
  let remaining = content;
  
  while (remaining.length > 0) {
    if (remaining.length <= suggestedMaxLength) {
      messages.push(remaining.trim());
      break;
    }
    
    const breakPoint = findBestBreakPoint(remaining, suggestedMaxLength);
    const message = remaining.substring(0, breakPoint).trim();
    
    if (message) messages.push(message);
    remaining = remaining.substring(breakPoint).trim();
  }
  
  return messages;
}

// 使用示例
const longMessage = "您好，关于您咨询的产品问题，我需要为您详细解释一下。首先，这款产品确实具有您提到的功能；其次，价格方面我们最近有优惠活动；最后，如果您现在下单的话，还可以享受免费配送服务。";

console.log(smartMessageSplit(longMessage));
// 输出可能是：
// [
//   "您好，关于您咨询的产品问题，我需要为您详细解释一下。",
//   "首先，这款产品确实具有您提到的功能；",
//   "其次，价格方面我们最近有优惠活动；",
//   "最后，如果您现在下单的话，还可以享受免费配送服务。"
// ]
```

**UI组件设计**：
- 卡片组件支持展开/收起状态
- 多条消息时显示进度指示器
- 发送过程中的加载状态和取消按钮
- 不同策略类型的视觉层次区分

---

### US-01-05: 资源发送建议
**优先级**: P1  
**估算**: M

**作为**客服人员  
**我想要**获得合适的资源发送建议  
**以便**及时向客户提供有价值的资料或优惠

**验收标准**：
- [ ] 独立的资源建议组件
- [ ] 根据用户意图智能推荐资源类型：
  - 资料包（PDF、文档等）
  - 优惠券
  - 活动链接
  - 课程资源
- [ ] 每个资源显示：
  - 资源图标
  - 资源名称
  - 资源描述（最多30字）
- [ ] 提供"一键发送"按钮
- [ ] 提供"换一个"按钮刷新推荐
- [ ] 支持资源预览功能
- [ ] 发送成功后显示确认提示

---

### US-01-06: AI助手对话框
**优先级**: P1  
**估算**: L

**作为**客服人员  
**我想要**与AI助手进行对话  
**以便**获得定制化的帮助和更多信息

**验收标准**：
- [ ] 独立的AI对话框组件（可收起/展开）
- [ ] 支持自然语言输入
- [ ] AI助手功能：
  - 重新生成回复建议
  - 询问客户详细信息
  - 获取产品知识
  - 查询历史对话
  - 分析客户行为
- [ ] 对话历史保存（当前会话）
- [ ] 支持快捷指令（如：/重新生成、/客户信息等）
- [ ] AI响应时间不超过3秒
- [ ] 支持中断正在生成的回复
- [ ] 提供有用/无用的反馈按钮

## 技术要求

### 前端技术栈
- 使用React/Vue组件化开发
- 使用WebSocket实现实时数据更新
- 使用状态管理工具（Redux/Vuex）管理数据
- 响应式设计，支持不同屏幕尺寸

### 后端集成
- RESTful API接口设计
- GraphQL支持复杂查询（可选）
- 消息队列处理AI分析任务
- 缓存机制优化性能

### AI服务集成
- NLP服务：意图识别、情绪分析
- 对话生成模型：回复建议
- 推荐算法：资源匹配

## 非功能性需求

### 性能要求
- 页面加载时间 < 3秒
- 实时数据更新延迟 < 5秒
- AI分析响应时间 < 3秒
- 支持并发用户数 > 1000

### 可用性要求
- 界面直观，新手培训时间 < 30分钟
- 支持键盘快捷键操作
- 支持深色/浅色主题切换
- 无障碍支持（WCAG 2.1 AA级）

### 安全要求
- 客户数据加密传输
- 敏感信息脱敏显示
- 操作日志记录
- 基于角色的访问控制

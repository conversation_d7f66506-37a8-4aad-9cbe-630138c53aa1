# 策略中心前端页面开发文档

## 1. 项目概述

### 1.1 功能定位
策略中心是一个基于Git理念的私域运营策略可视化管理系统，以生命周期模型为核心，通过表格化的方式管理用户在不同阶段的运营策略，支持版本控制和分支管理。

### 1.2 核心特性
- **生命周期策略表格**: 以用户生命周期为行，策略配置为列的表格结构
- **多类型单元格**: 支持文本、富文本、标签、下拉选择等多种单元格类型
- **版本控制**: 基于Git理念的分支管理和版本历史追踪
- **用户模型管理**: 支持多用户和多业务模型的策略配置

## 2. 技术架构

### 2.1 技术栈选择
基于项目现有技术栈和最佳实践推荐：

- **前端框架**: Vue 3.5.12 + Composition API
- **状态管理**: Vuex 4.1.0
- **路由**: Vue Router 4.4.5
- **样式系统**: TailwindCSS 3.4.13 + 现有设计系统
- **表格核心**: @tanstack/vue-table 8.20.5
- **图标系统**: Iconify (@iconify-json/*)
- **工具库**: @vueuse/core 12.0.0

### 2.2 组件库映射

| 功能需求 | 推荐组件 | 来源 | 说明 |
|---------|---------|------|------|
| 策略表格 | Table.vue | 现有components/table | 扩展现有组件 |
| 用户选择器 | FilterSelect | components-next/filter/inputs | 直接复用 |
| 分支选择器 | Button + DropdownMenu | components-next | 组合使用 |
| 按钮系统 | Button.vue | components-next/button | 直接复用 |
| 标签组件 | Badge | components-next/badge | 直接复用 |
| 富文本编辑 | ProseMirror | @chatwoot/prosemirror-schema | 现有依赖 |
| 模态框 | Dialog | @headlessui/vue | 新增依赖 |
| 图标 | Icon | components-next/icon | 直接复用 |
| 输入框 | Input | components-next/input | 直接复用 |
| 面包屑 | Breadcrumb | components-next/breadcrumb | 直接复用 |

## 3. 页面结构设计

### 3.1 路由配置
```javascript
// 在 captain.routes.js 中新增
{
  path: frontendURL('accounts/:accountId/captain/strategy-center'),
  component: StrategyCenterIndex,
  name: 'captain_strategy_center_index',
  meta: {
    permissions: ['administrator', 'agent'],
    featureFlag: FEATURE_FLAGS.CAPTAIN,
    installationTypes: [
      INSTALLATION_TYPES.CLOUD,
      INSTALLATION_TYPES.ENTERPRISE,
    ],
  },
}
```

### 3.2 菜单配置
```javascript
// 在 Sidebar.vue 的 Captain 菜单中新增
{
  name: 'Strategy Center',
  label: t('SIDEBAR.CAPTAIN_STRATEGY_CENTER'),
  to: accountScopedRoute('captain_strategy_center_index'),
}
```

### 3.3 页面布局结构
```
StrategyCenterIndex.vue
├── StrategyCenterHeader.vue          # 页面头部（面包屑导航）
├── StrategyCenterToolbar.vue         # 顶部工具栏
│   ├── UserModelSelector.vue         # 用户/模型选择器
│   └── CreateTaskButton.vue          # 新建任务按钮
├── BranchInfoBar.vue                 # 分支信息栏
│   ├── BranchSelector.vue            # 分支选择器（master）
│   ├── BranchStats.vue               # 分支统计（198 Branches, 341 Tags）
│   └── DataBranchButton.vue          # 数据分支按钮
└── StrategyTable.vue                 # 策略表格
    ├── StrategyTableHeader.vue       # 表格头部
    ├── StrategyTableBody.vue         # 表格主体
    ├── StrategyTableRow.vue          # 表格行
    ├── TableCells/                   # 单元格组件目录
    │   ├── LifecycleStageCell.vue    # 生命周期阶段单元格
    │   ├── TriggerCell.vue           # 触发条件单元格
    │   ├── TargetCell.vue            # 活动目标单元格
    │   ├── StrategyCell.vue          # 运营策略单元格（富文本）
    │   ├── AgentTagCell.vue          # 专家标签单元格
    │   ├── ImportanceCell.vue        # 重要性单元格
    │   └── ConstraintCell.vue        # 限制条件单元格
    └── AddRowButton.vue              # 新增行按钮
```

## 4. 核心组件设计

### 4.1 StrategyCenterToolbar.vue
**功能**: 顶部工具栏，包含用户选择器和新建任务按钮
**技术实现**:
```vue
<template>
  <div class="flex items-center justify-between p-4 bg-n-background border-b border-n-weak">
    <div class="flex items-center gap-4">
      <UserModelSelector
        v-model="selectedUser"
        :options="userOptions"
        placeholder="Wade Cooper"
        @update:model-value="handleUserChange"
      />
    </div>
    <div class="flex items-center gap-2">
      <Button
        variant="solid"
        color="blue"
        label="新建任务"
        @click="handleCreateTask"
      />
    </div>
  </div>
</template>
```

### 4.2 BranchInfoBar.vue
**功能**: 分支信息栏，显示分支选择器、统计信息和操作按钮
**技术实现**:
```vue
<template>
  <div class="flex items-center justify-between p-4 bg-n-background border-b border-n-weak">
    <div class="flex items-center gap-4">
      <!-- 分支选择器 -->
      <BranchSelector
        v-model="selectedBranch"
        :branches="branches"
        @update:model-value="handleBranchChange"
      />

      <!-- 分支统计 -->
      <BranchStats
        :branch-count="branchCount"
        :tag-count="tagCount"
      />
    </div>

    <div class="flex items-center gap-2">
      <Button
        variant="solid"
        color="blue"
        label="数据分支"
        @click="handleDataBranch"
      />
    </div>
  </div>
</template>
```

### 4.3 StrategyTable.vue
**功能**: 核心策略表格，展示生命周期各阶段的策略配置
**技术实现**:
```vue
<template>
  <div class="flex-1 flex flex-col bg-n-background">
    <!-- 策略表格 -->
    <div class="flex-1 overflow-auto">
      <table class="w-full border-collapse">
        <StrategyTableHeader :columns="tableColumns" />
        <StrategyTableBody>
          <StrategyTableRow
            v-for="row in strategyRows"
            :key="row.id"
            :row-data="row"
            @update="handleRowUpdate"
          >
            <LifecycleStageCell :value="row.stage" />
            <TriggerCell :value="row.trigger" />
            <TargetCell :value="row.target" />
            <StrategyCell :value="row.strategy" />
            <AgentTagCell :value="row.agent" />
            <ImportanceCell :value="row.importance" />
            <ConstraintCell :value="row.constraints" />
          </StrategyTableRow>
        </StrategyTableBody>
      </table>
    </div>

    <!-- 新增行按钮 -->
    <div class="p-4 border-t border-n-weak">
      <AddRowButton @click="handleAddRow" />
    </div>
  </div>
</template>
```

### 4.4 TableCell 组件系列
**功能**: 不同类型的单元格组件

#### 4.4.1 AgentTagCell.vue
**功能**: 专家标签单元格，显示不同类型的专家标签
```vue
<template>
  <div class="flex flex-wrap gap-1 p-2">
    <Badge
      :label="agentType"
      :color="getAgentColor(agentType)"
      variant="solid"
      size="sm"
    />
  </div>
</template>

<script setup>
const getAgentColor = (type) => {
  const colorMap = {
    '获客专家': 'blue',
    '互动专家': 'green',
    '转化专家': 'orange',
    '关怀专家': 'purple',
    '召回专家': 'red'
  };
  return colorMap[type] || 'gray';
};
</script>
```

#### 4.4.2 StrategyCell.vue
**功能**: 运营策略单元格，支持富文本内容
```vue
<template>
  <div class="p-2 min-h-[60px]">
    <div
      v-if="!isEditing"
      class="prose prose-sm"
      v-html="formattedStrategy"
      @click="startEditing"
    />
    <ProseMirror
      v-else
      v-model="editingValue"
      @blur="saveStrategy"
      @keydown.esc="cancelEditing"
    />
  </div>
</template>
```

### 4.5 BranchSelector.vue
**功能**: 分支选择器，显示当前分支和切换功能
**技术实现**:
```vue
<template>
  <div class="flex items-center gap-2">
    <Icon name="i-lucide-git-branch" class="text-n-slate-11 size-4" />
    <DropdownMenu>
      <template #trigger>
        <Button
          :label="selectedBranch.name"
          variant="ghost"
          icon="i-lucide-chevron-down"
          class="font-medium"
        />
      </template>

      <template #menu>
        <div class="py-1 min-w-48">
          <button
            v-for="branch in branches"
            :key="branch.id"
            class="flex items-center gap-2 w-full px-3 py-2 text-sm hover:bg-n-slate-2"
            :class="{
              'bg-n-blue-2 text-n-blue-11': branch.id === selectedBranch.id
            }"
            @click="handleBranchSelect(branch)"
          >
            <Icon name="i-lucide-git-branch" class="size-4" />
            {{ branch.name }}
            <Badge v-if="branch.isDefault" label="默认" size="xs" />
          </button>
        </div>
      </template>
    </DropdownMenu>
  </div>
</template>
```

## 5. 数据结构设计

### 5.1 策略表格数据结构
```typescript
interface StrategyTableData {
  id: string;
  userId: string;
  branchId: string;
  version: string;
  rows: StrategyRow[];
  metadata: {
    createdAt: string;
    updatedAt: string;
    author: string;
    commitMessage?: string;
  };
}

interface StrategyRow {
  id: string;
  stage: string;           // 生命周期阶段（引入期、成长期等）
  trigger: string;         // 触发条件
  target: string;          // 活动目标
  strategy: string;        // 运营策略（富文本）
  agent: string;           // 专家类型
  importance: string;      // 重要性
  constraints: string;     // 限制条件
  order: number;
}

interface TableColumn {
  id: string;
  label: string;
  type: 'text' | 'richtext' | 'tag' | 'select';
  width?: string;
}

// 表格列定义
const TABLE_COLUMNS: TableColumn[] = [
  { id: 'stage', label: '生命周期模型', type: 'text', width: '120px' },
  { id: 'trigger', label: '触发', type: 'text', width: '150px' },
  { id: 'target', label: '活动目标', type: 'text', width: '150px' },
  { id: 'strategy', label: '运营策略', type: 'richtext', width: '300px' },
  { id: 'agent', label: 'agent', type: 'tag', width: '120px' },
  { id: 'importance', label: '重要性', type: 'text', width: '120px' },
  { id: 'constraints', label: '限制条件', type: 'text', width: '200px' }
];
```

### 5.2 用户和分支数据结构
```typescript
interface User {
  id: string;
  name: string;
  avatar?: string;
  role: string;
  permissions: string[];
}

interface Branch {
  id: string;
  name: string;
  userId: string;
  isDefault: boolean;
  status: 'active' | 'inactive' | 'archived';
  metadata: {
    createdAt: string;
    createdBy: string;
    description?: string;
    lastCommit?: string;
  };
}

interface BranchStats {
  branchCount: number;
  tagCount: number;
  lastUpdated: string;
}

interface Commit {
  id: string;
  branchId: string;
  message: string;
  author: string;
  timestamp: string;
  changes: StrategyRow[];
}
```

## 6. 状态管理设计

### 6.1 Vuex Store 结构
```javascript
// store/modules/strategyCenter.js
const state = {
  // 当前选中的用户和分支
  selectedUser: null,
  selectedBranch: null,

  // 数据
  users: [],
  branches: [],
  strategyRows: [],
  branchStats: {
    branchCount: 0,
    tagCount: 0,
    lastUpdated: null
  },

  // UI状态
  isLoading: false,
  hasUnsavedChanges: false,

  // 模态框状态
  modals: {
    createTask: false,
    dataBranch: false,
    editStrategy: false,
  }
};

const mutations = {
  SET_SELECTED_USER(state, user) {
    state.selectedUser = user;
  },
  SET_SELECTED_BRANCH(state, branch) {
    state.selectedBranch = branch;
  },
  SET_STRATEGY_ROWS(state, rows) {
    state.strategyRows = rows;
  },
  SET_BRANCH_STATS(state, stats) {
    state.branchStats = stats;
  },
  SET_UNSAVED_CHANGES(state, hasChanges) {
    state.hasUnsavedChanges = hasChanges;
  },
  // ... 其他 mutations
};

const actions = {
  async fetchUsers({ commit }) {
    // 获取用户列表
  },
  async fetchBranches({ commit }, userId) {
    // 获取分支列表
  },
  async loadStrategyData({ commit }, { userId, branchId }) {
    // 加载策略数据
  },
  async saveStrategy({ commit }, { rows, commitMessage }) {
    // 保存策略并创建新版本
  },
  // ... 其他 actions
};
```

## 7. 交互流程设计

### 7.1 页面初始化流程
1. 加载当前用户信息和权限
2. 加载用户列表（如果有权限查看其他用户）
3. 选择默认用户（当前用户或上次选择的用户）
4. 加载该用户下的分支列表和统计信息
5. 选择默认分支（master或用户上次选择的分支）
6. 加载策略数据并渲染表格

### 7.2 策略编辑流程
1. 用户点击表格单元格进入编辑模式
2. 根据单元格类型显示对应的编辑器（文本框、富文本编辑器、标签选择器等）
3. 实时保存编辑内容到本地状态
4. 用户完成编辑后自动保存
5. 标记数据为已修改状态

### 7.3 新增行流程
1. 用户点击"新增行"按钮
2. 在表格底部添加新的空行
3. 自动设置生命周期阶段为下一个阶段
4. 用户填写各列的策略信息
5. 保存新增的策略行

### 7.4 分支切换流程
1. 用户点击分支选择器
2. 显示可用分支列表
3. 用户选择目标分支
4. 加载目标分支的策略数据
5. 更新分支统计信息

## 8. 开发计划

### 8.1 第一阶段：基础框架搭建（1-2周）
- [ ] 创建路由和菜单配置
- [ ] 搭建页面基础布局
- [ ] 实现顶部工具栏组件
- [ ] 实现分支信息栏组件

### 8.2 第二阶段：表格核心功能（2-3周）
- [ ] 实现策略表格基础结构
- [ ] 实现各种类型的单元格组件
  - [ ] 生命周期阶段单元格
  - [ ] 触发条件单元格
  - [ ] 活动目标单元格
  - [ ] 运营策略单元格（富文本）
  - [ ] 专家标签单元格
  - [ ] 重要性单元格
  - [ ] 限制条件单元格
- [ ] 实现表格的增删改查功能
- [ ] 实现新增行功能

### 8.3 第三阶段：数据管理功能（1-2周）
- [ ] 实现用户选择和切换
- [ ] 实现分支选择和切换
- [ ] 实现数据持久化
- [ ] 实现草稿自动保存

### 8.4 第四阶段：高级功能（1周）
- [ ] 实现新建任务功能
- [ ] 实现数据分支功能
- [ ] 实现批量操作
- [ ] 实现数据导入导出

### 8.5 第五阶段：优化和测试（1周）
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 单元测试编写
- [ ] 集成测试

## 9. 技术风险和解决方案

### 9.1 表格性能问题
**风险**: 大量数据时表格渲染性能下降
**解决方案**: 
- 使用虚拟滚动技术
- 实现分页加载
- 优化单元格组件渲染

### 9.2 状态管理复杂性
**风险**: 多层级状态管理导致代码复杂
**解决方案**:
- 合理拆分Vuex模块
- 使用Composition API简化状态逻辑
- 实现状态持久化

### 9.3 实时协作冲突
**风险**: 多用户同时编辑导致数据冲突
**解决方案**:
- 实现乐观锁机制
- 提供冲突解决界面
- 实时同步编辑状态

## 10. 总结

本文档基于实际UI设计图重新规划了策略中心前端页面的开发方案，采用单页面布局设计，以生命周期策略表格为核心，充分利用了Chatwoot项目现有的技术栈和组件库。

### 10.1 设计亮点
- **精确复现UI**: 完全按照设计图的布局和信息架构进行组件设计
- **单页面布局**: 采用简洁的单页面设计，提升用户体验
- **表格为中心**: 以7列策略表格为核心功能，支持多种单元格类型
- **组件化设计**: 模块化的组件架构，便于维护和扩展

### 10.2 技术优势
- **现有组件复用**: 最大化利用项目现有的components-next组件
- **设计系统一致**: 完全遵循Chatwoot现有的设计规范
- **渐进式开发**: 分阶段开发计划，确保项目按时交付
- **可扩展架构**: 为后续功能扩展预留了充足的空间

通过这个重新设计的方案，可以高效地实现一个功能完整、界面美观、用户体验良好的策略管理系统。
